import os
import json
from datetime import datetime
from dotenv import load_dotenv
from sqlalchemy import text, tuple_
from sqlalchemy.orm import Session
from app.models.slow_api_detail import SlowApiDetailORM
from app.integrations.tencent_cls.client import TencentCLSClient


load_dotenv()

def fetch_slow_api_details(query: str, start_time: int, end_time: int, db: Session) -> dict:
    """
    分批调用腾讯云CLS，批量写入慢接口明细表，返回target_date和插入条数。
    
    业务背景：
    用于采集慢API调用明细，支持大时间跨度自动分段采集，保证数据完整性和高效性。
    
    实现逻辑：
    1. 判断时间跨度，若大于6小时则拆分为4段，分别递归处理
    2. 否则按原有逻辑分页采集、写入
    3. 支持批量查重、批量upsert，提升写入效率
    
    典型用途：
    - 采集慢API明细数据，便于后续聚合、分析
    - 支持大批量日志数据的高效入库
    - 为慢API聚合、报表等提供原始数据
    
    :param query: CLS SQL查询语句
    :param start_time: 查询起始时间（时间戳，秒）
    :param end_time: 查询结束时间（时间戳，秒）
    :param db: SQLAlchemy数据库会话
    :return: {'target_date': str, 'inserted': int}
    """
    max_span = 6 * 3600  # 6小时
    total_inserted = 0
    if end_time - start_time > max_span:
        # 拆分为4段
        step = (end_time - start_time) // 4
        for i in range(4):
            seg_start = start_time + i * step
            seg_end = start_time + (i + 1) * step if i < 3 else end_time
            result = fetch_slow_api_details(query, seg_start, seg_end, db)
            total_inserted += result.get("inserted", 0)
        return {"target_date": str(datetime.fromtimestamp(start_time).date()), "inserted": total_inserted}
    REGION = os.getenv('REGION', 'ap-guangzhou')
    TOPIC_ID = os.getenv('TOPIC_ID', 'YOUR_TOPIC_ID')

    client = TencentCLSClient()
    context = ""
    target_date = datetime.fromtimestamp(start_time).date()
    last_context = None
    while True:
        resp = client.search_log(query, start_time, end_time, limit=100, context=context)
        resp_json = json.loads(resp.to_json_string())
        batch = []
        for item in resp_json.get("AnalysisResults", []):
            data = {d["Key"]: d["Value"] for d in item["Data"]}
            collect_time_str = data.get("collect_time")
            collect_time = datetime.strptime(collect_time_str, "%Y-%m-%d %H:%M") if collect_time_str else None
            path = data.get("path")
            x_b3_traceid = data.get("x_b3_traceid")
            duration = float(data.get("duration")) if data.get("duration") else None
            batch.append({
                "target_date": target_date,
                "collect_time": collect_time,
                "path": path,
                "x_b3_traceid": x_b3_traceid,
                "duration": duration
            })
        if not batch:
            break
        # 批量查重
        keys = set((item["target_date"], item["x_b3_traceid"], item["duration"]) for item in batch)
        existing = db.query(
            SlowApiDetailORM.target_date,
            SlowApiDetailORM.x_b3_traceid,
            SlowApiDetailORM.duration
        ).filter(
            tuple_(
                SlowApiDetailORM.target_date,
                SlowApiDetailORM.x_b3_traceid,
                SlowApiDetailORM.duration
            ).in_(keys)
        ).all()
        existing_set = set(existing)
        # 只插入不存在的
        to_insert = [
            SlowApiDetailORM(
                target_date=item["target_date"],
                collect_time=item["collect_time"],
                path=item["path"],
                x_b3_traceid=item["x_b3_traceid"],
                duration=item["duration"]
            )
            for item in batch
            if (item["target_date"], item["x_b3_traceid"], item["duration"]) not in existing_set
        ]
        # 批量 upsert 替换原有 bulk_save_objects，唯一冲突时自动更新
        from sqlalchemy.dialects.mysql import insert
        data_list = []
        for obj in to_insert:
            data_list.append({
                "target_date": obj.target_date,
                "collect_time": obj.collect_time,
                "path": obj.path,
                "x_b3_traceid": obj.x_b3_traceid,
                "duration": obj.duration,
                # 不传 created_at 和 updated_at
            })
        if data_list:
            BATCH_SIZE = 500  # 每批 upsert 500 条
            for i in range(0, len(data_list), BATCH_SIZE):
                batch = data_list[i:i+BATCH_SIZE]
                stmt = insert(SlowApiDetailORM).values(batch)
                update_dict = {
                    "collect_time": stmt.inserted.collect_time,
                    "path": stmt.inserted.path,
                    "duration": stmt.inserted.duration,
                    "updated_at": text("NOW()"),
                }
                stmt = stmt.on_duplicate_key_update(**update_dict)
                db.execute(stmt)
            db.commit()
        total_inserted += len(to_insert)
        # 死循环保护
        new_context = resp_json.get("Context", "")
        if resp_json.get("ListOver", True):
            break
        if new_context == last_context:
            break
        context = new_context
        last_context = context
    return {"target_date": str(target_date), "inserted": total_inserted}